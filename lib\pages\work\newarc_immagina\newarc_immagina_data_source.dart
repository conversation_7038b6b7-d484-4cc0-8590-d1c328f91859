import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/immaginaProject.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/common_utils.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/utils/storage.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/tab/popup_menu_button.dart';
import 'package:newarc_platform/widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/widget/UI/tab/tag_widget.dart';
import 'package:newarc_platform/widget/UI/tab/users_stack_list.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;


import '../../../widget/UI/tab/common_icon_button.dart';
import '../../../widget/UI/tab/text_button.dart';

class RequestSource extends DataTableSource {
  RequestSource({
    this.onAddressTap,
    this.onAgencyNameTap,
    this.isRequest = false,
    required this.projects,
  });

  List<ImmaginaProject> projects = [];
  bool? isRequest;

  Function(ImmaginaProject)? onAddressTap;
  Function(dynamic)? onAgencyNameTap;

  List<Map> priorityStatus = [
    {
      'value': 'normale',
      'label': 'Normale',
      'bgColor': Color(0xFFFFFBEC),
      'boarderColor': Color(0xFFFFE9A0),
      'textColor': Color(0xFFFFB700)
    },
    {
      'value': 'alta',
      'label': 'Alta',
      'bgColor': Color(0xffFDEEEE),
      'boarderColor': Color(0xFFFFC9C9),
      'textColor': Color(0xFFE82525)
    },
    {
      'value': 'bassa',
      'label': 'Bassa',
      'bgColor': Color(0xffECFFEF),
      'boarderColor': Color(0xFFC3F7CB),
      'textColor': Color(0xFF39C14F)
    },
  ];

  @override
  DataRow? getRow(int index) {
    if (index < projects.length) {
      final project = projects[index];
      final projectStatus = project.requestStatus.toLowerCase();
      bool isNew = ((project.statusChangedDate != null && DateTime.now().millisecondsSinceEpoch - project.statusChangedDate! <= 24 * 60 * 60 * 1000) && projectStatus != CommonUtils.bloccata) && isRequest == true;

      var address = isRequest == true
                        ? "${project.streetName ?? 'noStreetName'}, ${project.streetNumber ?? 'noStreetNum'}"
                        : "${project.streetName ?? 'noStreetName'}, ${project.streetNumber ?? 'noStreetNum'} ${project.housingUnit != null ? '-' : ''} ${project.housingUnit ?? ''}";
      if (project.addressInfo != null) {
        address = "${project.addressInfo!.streetName ?? 'noStreetName'} ${project.addressInfo!.streetNumber ?? 'noStreetNum'}, ${project.addressInfo!.city ?? 'noCity'} ${project.housingUnit != null ? '-' : ''} ${project.housingUnit ?? ''}";
      }

      var priorityData = {};
      if(project.priority?.isNotEmpty ?? false){
        priorityData = priorityStatus.firstWhere(
              (element) => element['value'] == project.priority?.toLowerCase(),
        );
      }

      bool isProfessionals = project.isForProfessionals ?? false;
      bool isFromProfessional = project.professionalId != null ? true : false;
      bool isSmart = project.isSmart;

      return DataRow(
        color: MaterialStateProperty.resolveWith<Color?>(
          (Set<MaterialState> states) {
            int daysPassed = calculateDaysPassed(project.statusChangedDate,maxDayCount: 5);
            print(daysPassed);
            if (projectStatus == CommonUtils.inLavorazione && daysPassed > 4) {
              return const Color(0xFFDD0000).withOpacity(0.1);
            }

            if (isNew) {
              return const Color(0xFFFEC600).withOpacity(0.1);
            }

            return null;
          },
        ),
        cells: [
          ///Codice
          DataCell(
            Stack(
              clipBehavior: Clip.none,
              children: [
                if (isProfessionals || isSmart)
                Positioned(
                  top: -24,
                  child: Row(
                    children: [
                      TagWidget(
                        text: isProfessionals ? "Professionals" : "Smart",
                        statusColor: isProfessionals ? Colors.black : null,
                        changingColors : isSmart ? [
                          Color(0xff5abdb5),
                          Color(0xff499b79),
                          Color(0xff145935),
                        ] : null,
                      ),
                      SizedBox(width: 5,),
                      isNew
                      ? TagWidget(
                        text: "Novità",
                        statusColor: Color(0xffFEC600),
                      )
                      : SizedBox.shrink(),
                    ],
                  ),
                ),
                if (isNew && !(isProfessionals || isSmart))
                Positioned(
                  top: -24,
                  child: TagWidget(
                    text: "Novità",
                    statusColor: Color(0xffFEC600),
                  ),
                ),
                NarFormLabelWidget(
                  label: project.projectId,
                  overflow: TextOverflow.ellipsis,
                  fontSize: 12,
                  fontWeight: '600',
                  textColor: AppColor.black,
                ),
              ],
            ),
          ),

          ///Priorità
          if (isRequest == false)
            DataCell(
              project.priority?.isNotEmpty ?? false ?
              SizedBox(
                width: 70,
                height: 27,
                child: TextButtonWidget(
                  status: project.priority?.toCapitalized(),
                  containerAlignment: Alignment.center,
                  textColor: priorityData["textColor"],
                  isOnlyBorder: true,
                  borderColor: priorityData["boarderColor"],
                  backgroundColor: priorityData["bgColor"],
                  borderRadius: 5,
                  textSize: 11,
                  textWeight: '600',
                ),
              ) : SizedBox.shrink(),
            ),

          ///Indirizzo
          DataCell(
            NarLinkWidget(
              text: address,
              textColor: Colors.black,
              fontWeight: '700',
              fontSize: 12,
              overflow: TextOverflow.ellipsis,
              onClick: () {
                onAddressTap!(project);
              },
            ),
          ),

          ///Team
          if(isRequest==false)
          DataCell(
            FutureBuilder<List<String>>(
              future: Future.wait(
                project.renderistTeamIdList.where((user) => (user!=null && user!='') ).map((user) => getImageUrl(user)).toList(),
              ),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return Container(
                    height: 30,
                    width: 150,
                    alignment: Alignment.centerLeft,
                    child: UsersStackWidget(
                      imageList: snapshot.data ?? [],
                      radius: 25,
                      itemCount: 7,
                    ),
                  );
                } else if (snapshot.hasError) {
                  return Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: Colors.grey,
                    ),
                  );
                }
                return SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 1,
                  ),
                );
              },
            ),
          ),

          ///Agenzia
          DataCell(
            FutureBuilder<DocumentSnapshot>(
              future: isFromProfessional == false
                ? FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES).doc(project.agencyId).get()
                : FirebaseFirestore.instance.collection(appConfig.COLLECT_PROFESSIONALS).doc(project.professionalId).get(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return NarFormLabelWidget(
                    label: 'Loading...',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 12,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  );
                }
                if (snapshot.hasError || !snapshot.hasData || !snapshot.data!.exists) {
                  return NarFormLabelWidget(
                    label: '',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 12,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  );
                }
                Map ownerData = snapshot.data!.data()! as Map<String, dynamic>;
                if (isFromProfessional) {
                  return IconButtonWidget(
                    iconPadding: EdgeInsets.all(4),
                    onTap: () {
                      onAgencyNameTap!(ownerData["contactPersonInfo"]);
                    },
                    isSvgIcon: true,
                    icon: 'assets/icons/agency-action.svg',
                    iconColor: AppColor.greyColor,
                  );
                } else {
                  return IconButtonWidget(
                    iconPadding: EdgeInsets.all(4),
                    onTap: () {
                      onAgencyNameTap!(ownerData);
                    },
                    isSvgIcon: true,
                    icon: 'assets/icons/agency-action.svg',
                    iconColor: Colors.blueGrey,
                  );
                }
              },
            ),
          ),

          ///Richiesta
          DataCell(
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (project.wantsNewarcPictures) ...[
                  NarFormLabelWidget(
                    label: 'Foto',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 11,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  ),
                ],
                if (project.wantsNewarcPlanimetry) ...[
                  NarFormLabelWidget(
                    label: 'Planimetria',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 11,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  ),
                ],
                if (project.isForProfessionals && (project.optionals?.isNotEmpty ?? false)) ...[
                  NarFormLabelWidget(
                    label: 'Optionals',
                    overflow: TextOverflow.ellipsis,
                    fontSize: 11,
                    fontWeight: '600',
                    textColor: AppColor.black,
                  ),
                ],
                NarFormLabelWidget(
                  label: 'Progetto',
                  overflow: TextOverflow.ellipsis,
                  fontSize: 11,
                  fontWeight: '600',
                  textColor: AppColor.black,
                ),
              ],
            ),
          ),

          ///request -> Data richiesta project -> Accettazione
          // if (isRequest == true) ...[
          //   DataCell(
          //     NarFormLabelWidget(
          //       label: project.receivedPaymentDate != null ? DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(project.receivedPaymentDate ?? 0)).toString() : '',
          //       overflow: TextOverflow.ellipsis,
          //       fontSize: 12,
          //       fontWeight: '600',
          //       textColor: AppColor.black,
          //     ),
          //   ),
          // ] else ...[
            ///Accettazione
            DataCell(
              NarFormLabelWidget(
                label: project.statusChangedDate != null ? DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(project.statusChangedDate ?? 0)).toString() : '',
                overflow: TextOverflow.ellipsis,
                fontSize: 12,
                fontWeight: '600',
                textColor: AppColor.black,
              ),
            ),
          // ],

          ///request -> Stato || project -> Realizzazione
          if (isRequest == true) ...[
            DataCell(
              Row(
                children: [
                  StatusWidget(
                    status: projectStatus == CommonUtils.inAnalisi ? "${projectStatus} - ${calculateDaysPassed(project.statusChangedDate,maxDayCount: 3)}/3" : projectStatus,
                    statusColor: CommonUtils.getColor(projectStatus),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  if (projectStatus == CommonUtils.bloccata)
                    PopupMenuOnHover(
                      note: project.blockedNotes,
                      reason: project.blockedSection ?? "",
                      isForProfessionals: project.isForProfessionals ?? false,
                      isSmart: project.isSmart ?? false,
                    ),
                ],
              ),
            ),
          ] else ...[
            DataCell(
              StatusWidget(
                status: projectStatus == CommonUtils.inLavorazione ? "${projectStatus} - ${calculateDaysPassed(project.statusChangedDate,maxDayCount: 5)}/5" : projectStatus,
                statusColor: CommonUtils.getColor(projectStatus),
              ),
            ),
          ],

          ///project -> Vendita
          if (isRequest == false)
            DataCell(
              StatusWidget(
                status: (project.isAgencyArchived ?? false) ? project.isHouseSold  ? CommonUtils.venduto : CommonUtils.nonVenduto : "",
                statusColor: (project.isAgencyArchived ?? false) ? project.isHouseSold  ? Color(0xff39C14F) : Color(0xffDD0000) : null,
              ),
            ),
        ],
      );
    }

    return null;
  }

  int calculateDaysPassed(int? statusChangedDate, {required int maxDayCount}) {
    if (statusChangedDate == null) return 0;

    DateTime startDate = onlyDate(DateTime.fromMillisecondsSinceEpoch(statusChangedDate)).add(Duration(days: 1));
    DateTime now = onlyDate(DateTime.now());

    int workingDays = 0;
    DateTime currentDay = startDate;

    while (workingDays < maxDayCount && (currentDay.isBefore(now) || currentDay.isAtSameMomentAs(now))) {
      if (currentDay.weekday != DateTime.saturday && currentDay.weekday != DateTime.sunday) {
        workingDays++;
      }
      currentDay = currentDay.add(Duration(days: 1));
    }

    return workingDays;
  }

  DateTime onlyDate(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  Future<String> getImageUrl(String userId) async {
    final extensions = ['.jpeg', '.png', '.jpg'];
    for (final extension in extensions) {
      final ref = FirebaseStorage.instance.ref().child('users/$userId/profile$extension');
      try {
        return await ref.getDownloadURL();
      } catch (error) {
        continue;
      }
    }
    print('Profile image not found for user $userId');
    return await printUrl('utils/', "", "imageplaceholder.png");
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => projects.length;

  @override
  int get selectedRowCount => 0;
}


